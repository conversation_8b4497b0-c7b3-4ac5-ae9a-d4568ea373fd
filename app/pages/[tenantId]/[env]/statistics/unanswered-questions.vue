<script setup lang="ts">
import { debounce } from 'lodash'

const reportsStore = useReportsStore()
const ragsStore = useRagsStore()

const {
  range,
  unansweredReportsData,
  unansweredReportFilter,
  selectedCategories,
  loadings,
  unansweredReportsPagination,
  unansweredReportsDataTotal
} = storeToRefs(reportsStore)

onMounted(async () => {
  await ragsStore.ragTenantLogin()
  reportsStore.getUnansweredReport()
})

const refresh = async () => {
  await reportsStore.getUnansweredReport()
}
const debounceRefresh = debounce(refresh, 1000)
watch(
  () => range.value,
  async () => {
    refresh()
    selectedCategories.value = []
  }
)
watch(
  () => unansweredReportsPagination.value.pageCount,
  () => {
    unansweredReportsPagination.value.page = 1
  }
)

watch(
  () => unansweredReportsPagination.value,
  () => {
    refresh()
  },
  { deep: true }
)
watch(
  () => [
    unansweredReportFilter.value.context_type,
    unansweredReportFilter.value.analyzed_action,
    unansweredReportFilter.value.processed
  ],
  async () => {
    await refresh()
    selectedCategories.value = []
  }
)
watch(
  () => [
    unansweredReportFilter.value.session_id,
    unansweredReportFilter.value.chat_id
  ],
  () => {
    debounceRefresh()
  }
)

// Watch for sort changes and update pagination asc property
const sort = ref({ column: 'query_date', direction: 'desc' as 'asc' | 'desc' })
watch(
  () => sort.value,
  (newSort) => {
    // Update the asc property based on sort direction
    unansweredReportsPagination.value.asc = newSort.direction === 'asc'
    // Reset to page 1 when sorting changes
    unansweredReportsPagination.value.page = 1
  },
  { deep: true }
)
const defaultColumns = [
  {
    key: 'session_id',
    label: 'セッションID',
    sortable: false
  },
  {
    key: 'chat_id',
    label: 'チャットID',
    sortable: false
  },
  {
    key: 'analyzed_action',
    label: 'パターン',
    sortable: false
  },
  {
    key: 'context_type',
    label: 'コンテキスト',
    sortable: false
  },
  {
    key: 'processed',
    label: '処理済み',
    sortable: false
  },
  {
    key: 'query',
    label: '質問',
    sortable: false
  },
  {
    key: 'query_date',
    label: '質問日時',
    sortable: true
  }
]

const defaultProcessedType = [
  { label: '正常', value: true },
  { label: '失敗', value: false }
]
enum LlmAnalyzeNextAction {
  NORMAL = 1,
  RAG = 2,
  SUMMARY = 3,
  WEATHER = 101,
  CASHE = 199
}

enum ContextType {
  KNOWLEDGE = 1,
  WEBSEARCH = 2,
  UNKNOWN = 0,
  NOKNOWLEDGEMATCH = 99
}

const defaultContextTypeUses = [
  { label: 'ナレッジ', value: ContextType.KNOWLEDGE },
  { label: 'ウェブ検索', value: ContextType.WEBSEARCH },
  { label: '以前の記録不明', value: ContextType.UNKNOWN },
  { label: '対応ナレッジなし', value: ContextType.NOKNOWLEDGEMATCH }
]

const defaultAnalyzedActionTypeUses = [
  { label: '通常', value: LlmAnalyzeNextAction.NORMAL },
  { label: 'RAG', value: LlmAnalyzeNextAction.RAG },
  { label: 'まとめ', value: LlmAnalyzeNextAction.SUMMARY },
  { label: '天気', value: LlmAnalyzeNextAction.WEATHER },
  { label: 'キャッシュ', value: LlmAnalyzeNextAction.CASHE }
]

const items = [
  [
    {
      label: 'CSV出力',
      icon: 'ph:file-csv-light',
      click: () =>
        exportUnansweredQuestionsToCSV(
          selectedCategories.value,
          'unanswered-question-logs'
        )
    }
  ]
]

const exportUnansweredQuestionsToCSV = (rows: any[], documentName?: string) => {
  reportsStore.exportUnansweredQuestionsToCSV(rows, documentName)
}

const rowMenus = (row: any) => {
  return [
    [
      {
        label: 'CSV出力 ',
        icon: 'ph:file-csv-light',
        click: () => exportUnansweredQuestionsToCSV([row], row?.query)
      }
    ]
  ]
}

const showAdvancedFilters = ref(false)

const showAdvancedSearch = ref(false)
</script>

<template>
  <UDashboardPage>
    <UDashboardPanel grow>
      <UDashboardNavbar
        title="未回答の質問"
        :badge="unansweredReportsDataTotal"
      />

      <UDashboardToolbar>
        <template #left>
          <div class="flex gap-5">
            <BaseDateRangePicker
              v-model="range"
              class="-ml-2.5"
              :exclude-today="true"
            />
          </div>
        </template>
        <template #right>
          <div class="flex items-center gap-1.5">
            <div />
            <div v-if="selectedCategories.length">
              <UDropdown
                :items="items"
                :popper="{ placement: 'bottom-start' }"
              >
                <UButton
                  color="white"
                  :label="`一括操作（${selectedCategories.length}件）`"
                  icon="fluent:form-multiple-20-regular"
                  trailing-icon="i-heroicons-chevron-down-20-solid"
                  size="sm"
                />
              </UDropdown>
            </div>
            <div class="flex gap-2">
              <UButton
                :icon="'mage:filter'"
                color="white"
                size="sm"
                label="高度な検索"
                @click="showAdvancedSearch = !showAdvancedSearch"
              />
              <UButton
                icon="prime:sync"
                color="gray"
                size="sm"
                @click="refresh"
              />
            </div>
          </div>
        </template>
      </UDashboardToolbar>
      <UDashboardToolbar v-if="showAdvancedFilters">
        <div class="flex flex-row gap-2">
          <UInput
            v-model="unansweredReportFilter.session_id"
            icon="i-heroicons-magnifying-glass-20-solid"
            autocomplete="off"
            size="sm"
            placeholder="セッションID"
            @keydown.esc="$event.target.blur()"
          />
          <UInput
            v-model="unansweredReportFilter.chat_id"
            icon="i-heroicons-magnifying-glass-20-solid"
            autocomplete="off"
            size="sm"
            placeholder="チャットID"
            @keydown.esc="$event.target.blur()"
          />
          <USelectMenu
            v-model="unansweredReportFilter.context_type"
            placeholder="ナレッジの種類"
            class="w-44"
            selected-icon="i-heroicons-check-circle"
            :options="defaultContextTypeUses"
            :ui-menu="{ option: { base: 'capitalize' } }"
          >
            <template #label>
              <div v-if="unansweredReportFilter.context_type">
                {{ unansweredReportFilter.context_type.label }}
              </div>
              <div v-else>
                ナレッジの種類
              </div>
            </template>
            <template
              v-if="unansweredReportFilter.context_type"
              #trailing
            >
              <UButton
                v-if="defaultContextTypeUses"
                size="xs"
                icon="i-lucide-delete"
                color="gray"
                :class="[
                  'ml-2 px-2 py-1 rounded  hover:text-red-600 !pointer-events-auto'
                ]"
                @click.stop="
                  () => {
                    unansweredReportFilter.context_type = null;
                  }
                "
              />
            </template>
          </USelectMenu>

          <USelectMenu
            v-model="unansweredReportFilter.analyzed_action"
            placeholder="RAGパターン"
            selected-icon="i-heroicons-check-circle"
            class="w-40"
            :options="defaultAnalyzedActionTypeUses"
            :ui-menu="{ option: { base: 'capitalize' } }"
          >
            <template #label>
              <div v-if="unansweredReportFilter.analyzed_action">
                {{ unansweredReportFilter.analyzed_action.label }}
              </div>
              <div v-else>
                RAGパターン
              </div>
            </template>
            <template
              v-if="unansweredReportFilter.analyzed_action"
              #trailing
            >
              <UButton
                v-if="defaultAnalyzedActionTypeUses"
                size="xs"
                icon="i-lucide-delete"
                color="gray"
                :class="[
                  'ml-2 px-2 py-1 rounded  hover:text-red-600 !pointer-events-auto'
                ]"
                @click.stop="
                  () => {
                    unansweredReportFilter.analyzed_action = null;
                  }
                "
              />
            </template>
          </USelectMenu>
          <USelectMenu
            v-model="unansweredReportFilter.processed"
            placeholder="正常処理済み"
            class="w-40"
            :options="defaultProcessedType"
            :ui-menu="{ option: { base: 'capitalize' } }"
          >
            <template #label>
              <div v-if="unansweredReportFilter.processed">
                {{ unansweredReportFilter.processed.label }}
              </div>
              <div v-else>
                正常処理済み
              </div>
            </template>
            <template
              v-if="unansweredReportFilter.processed"
              #trailing
            >
              <UButton
                v-if="defaultProcessedType"
                size="xs"
                icon="i-lucide-delete"
                color="gray"
                :class="[
                  'ml-2 px-2 py-1 rounded  hover:text-red-600 !pointer-events-auto'
                ]"
                @click.stop="
                  () => {
                    unansweredReportFilter.processed = null;
                  }
                "
              />
            </template>
          </USelectMenu>
        </div>
      </UDashboardToolbar>
      <UTable
        v-if="unansweredReportsData"
        v-model="selectedCategories"
        v-model:sort="sort"
        :rows="unansweredReportsData"
        :columns="defaultColumns"
        :loading="loadings.getUnansweredReport"
        :sort-asc-icon="undefined"
        :sort-desc-icon="undefined"
        class="w-full"
        :ui="{
          divide: 'divide-gray-200 dark:divide-gray-800',
          tr: { base: 'group' }
        }"
      >
        <template #query_date-data="{ row }">
          <div class="flex items-center gap-3 justify-between">
            {{ formatDateTime(row.query_date) }}
            <UDropdown
              class="invisible group-hover:visible"
              :items="rowMenus(row)"
              :popper="{ placement: 'bottom-start' }"
            >
              <UButton
                class="row-menu"
                color="white"
                icon="charm:menu-meatball"
                size="xs"
                square
                :loading="false"
              />
            </UDropdown>
          </div>
        </template>
        <template #analyzed_action-data="{ row }">
          <div class="w-24">
            <UBadge
              size="sm"
              variant="subtle"
              :trailing="false"
              v-bind="reportRagObject(row.analyzed_action)"
            />
          </div>
        </template>
        <template #context_type-data="{ row }">
          <div class="w-40">
            <UBadge
              size="sm"
              variant="subtle"
              :trailing="false"
              v-bind="reportContextObject(row.context_type)"
            />
          </div>
        </template>
        <template #processed-data="{ row }">
          <div class="w-40">
            <UBadge
              size="sm"
              variant="subtle"
              :trailing="false"
              v-bind="reportProcessedObject(row.processed)"
            />
          </div>
        </template>
      </UTable>
      <UDivider class="mt-0" />
      <UDashboardToolbar>
        <template #left>
          <div class="flex items-center gap-1.5">
            <span class="text-sm leading-5">表示件数:</span>

            <USelect
              v-model="unansweredReportsPagination.pageCount"
              :options="[3, 5, 10, 20, 30, 40]"
              class="w-20"
            />
          </div>
        </template>

        <template #right>
          <UPagination
            v-if="
              unansweredReportsPagination.pageCount < unansweredReportsDataTotal
            "
            v-model="unansweredReportsPagination.page"
            :page-count="unansweredReportsPagination.pageCount"
            :total="unansweredReportsDataTotal"
            size="sm"
            :ui="{
              wrapper: 'flex items-center gap-1',
              rounded: '!rounded-full min-w-[32px] justify-center',
              default: {
                activeButton: {
                  variant: 'outline'
                }
              }
            }"
          />
        </template>
      </UDashboardToolbar>
    </UDashboardPanel>

    <BaseAdvancedSearch
      :show="showAdvancedSearch"
      @close="showAdvancedSearch = false"
    />
  </UDashboardPage>
</template>
