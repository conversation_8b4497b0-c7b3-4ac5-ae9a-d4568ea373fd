<script setup lang="ts">
import { sub, format, isSameDay, type Duration, add } from 'date-fns'
import { ja } from 'date-fns/locale'
import type { Range } from '~/types'

const emits = defineEmits(['updated'])

const props = defineProps({
  size: {
    type: String as PropType<'xs' | 'sm' | 'md' | 'lg'>,
    default: 'md'
  },
  mini: {
    type: Boolean,
    default: false
  },
  excludeToday: {
    type: Boolean,
    default: false
  }
})
const ranges = [
  { label: props.excludeToday ? '昨日' : '今日', duration: { days: 0 } },
  { label: '最近の3日', duration: { days: 3 } },
  { label: '最近の7日', duration: { days: 7 } },
  { label: '最近の30日', duration: { days: 30 } },
  { label: '最近の90日', duration: { days: 90 } },
  { label: '最近の1年', duration: { years: 1 } }
]
const selected = defineModel({
  type: Object as PropType<Range>,
  required: true
})

function isRangeSelected(duration: Duration) {
  return (
    isSameDay(selected.value.start, sub(new Date(), duration))
    && isSameDay(selected.value.end, new Date())
  )
}

function selectRange(duration: Duration) {
  const start = props.excludeToday && duration.days === 0
    ? sub(new Date(), { days: 1 })
    : sub(new Date(), duration)

  const end = props.excludeToday
    ? sub(new Date(), { days: 1 })
    : new Date()

  selected.value = { start, end }
  emits('updated', selected.value)
}

const disabledDates = ref([
  // Disable all dates after today
  { start: add(new Date(), { days: props.excludeToday ? 0 : 1 }) }
  // disable all dates before 3 months ago
  // { end: sub(sub(new Date(), { months: 3 }), { days: 1 }) }
])

const formatString = computed(() => {
  return props.mini ? 'M月d日' : 'yyyy年MM月dd日'
})
</script>

<template>
  <UPopover :popper="{ placement: 'bottom-start' }">
    <template #default="{ open }">
      <UButton
        color="gray"
        variant="ghost"
        :class="[open && 'bg-gray-50 dark:bg-gray-800']"
        :trailing-icon="'i-heroicons-chevron-down-20-solid'"
        :size="size"
        v-bind="$attrs"
      >
        <div>
          {{ format(selected.start, formatString, { locale: ja }) }} →
          {{ format(selected.end, formatString, { locale: ja }) }}
        </div>
      </UButton>
    </template>

    <template #panel="{ close }">
      <div
        class="flex items-center sm:divide-x divide-gray-200 dark:divide-gray-800"
      >
        <div class="hidden sm:flex flex-col py-4">
          <UButton
            v-for="(range, index) in ranges"
            :key="index"
            :label="range.label"
            color="gray"
            variant="ghost"
            class="rounded-none px-6"
            :class="[
              isRangeSelected(range.duration)
                ? 'bg-gray-100 dark:bg-gray-800'
                : 'hover:bg-gray-50 dark:hover:bg-gray-800/50'
            ]"
            truncate
            @click="selectRange(range.duration)"
          />
        </div>
        <div>
          <div
            v-if="mini"
            class="text-base text-gray-500 dark:text-gray-300 dark:bg-gray-900 py-2 px-4 text-start border-b border-gray-200 dark:border-gray-800"
          >
            {{ format(selected.start, "yyyy年MM月dd日", { locale: ja }) }} →
            {{ format(selected.end, "yyyy年MM月dd日", { locale: ja }) }}
          </div>
          <DatePicker
            v-model="selected"
            locale="ja"
            :disabled-dates="disabledDates"
            @close="close"
            @update:model-value="emits('updated', $event)"
          />
        </div>
      </div>
    </template>
  </UPopover>
</template>
