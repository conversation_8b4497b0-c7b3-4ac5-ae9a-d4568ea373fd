<template>
  <UInput
    autocomplete="uuid"
    v-bind="$attrs"
    :ui="{ icon: { trailing: { pointer: '' } } }"
  >
    <template #trailing>
      <UButton
        color="gray"
        variant="link"
        size="xs"
        icon="fluent:clipboard-paste-16-filled"
        :padded="false"
        label="貼り付け"
        @click="onPaste"
      />
    </template>
  </UInput>
</template>

<script setup lang="ts">
const emit = defineEmits(['update:modelValue'])

const onPaste = async () => {
  const text = await navigator.clipboard.readText()
  emit('update:modelValue', text)
}
</script>
