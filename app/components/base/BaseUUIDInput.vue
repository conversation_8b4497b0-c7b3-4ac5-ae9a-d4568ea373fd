<template>
  <UInput
    autocomplete="uuid"
    v-bind="$attrs"
    :ui="{ icon: { trailing: { pointer: '' } } }"
  >
    <template #trailing>
      <UButton
        color="gray"
        variant="link"
        size="xs"
        icon="fluent:clipboard-paste-16-filled"
        :padded="false"
        label="貼り付け"
        @click="isShowPassword = !isShowPassword"
      />
    </template>
  </UInput>
</template>

<script setup lang="ts">
const isShowPassword = ref(false)

const props = defineProps({
  showPassword: {
    type: Boolean,
    default: false
  }
})

onMounted(() => {
  isShowPassword.value = props.showPassword
})

watch(
  () => props.showPassword,
  (value) => {
    isShowPassword.value = value
  }
)
</script>
