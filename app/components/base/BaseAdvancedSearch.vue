<script setup lang="ts">
import { z } from 'zod'
import type { FormSubmitEvent } from '#ui/types'

// UUID validation regex
const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i

// Zod schema for advanced search form
const advancedSearchSchema = z.object({
  range: z.object({
    start: z.date(),
    end: z.date()
  }),
  session_id: z
    .string()
    .optional()
    .nullable()
    .refine(
      val => !val || val.trim() === '' || uuidRegex.test(val.trim()),
      {
        message: 'セッションIDは有効なUUID形式である必要があります'
      }
    ),
  chat_id: z
    .string()
    .optional()
    .refine(
      val => !val || val.trim() === '' || uuidRegex.test(val.trim()),
      {
        message: 'チャットIDは有効なUUID形式である必要があります'
      }
    ),
  context_type: z.string().optional(),
  analyzed_action: z.string().optional(),
  processed: z.string().optional(),
  knowledge_type: z.string().optional(),
  rag_pattern: z.string().optional(),
  normal_processed: z.string().optional()
})

const props = defineProps({
  show: Boolean,
  targets: Array as PropType<any[]>
})
const toast = useToast()

// Initialize with default date range (last 7 days)
const today = new Date()
const sevenDaysAgo = new Date()
sevenDaysAgo.setDate(today.getDate() - 7)

const advancedSearch = reactive({
  range: {
    start: sevenDaysAgo,
    end: today
  },
  session_id: '',
  chat_id: '',
  context_type: '',
  analyzed_action: '',
  processed: '',
  knowledge_type: '',
  rag_pattern: '',
  normal_processed: ''
})

// Options for knowledge types (based on context types)
const knowledgeTypeOptions = [
  { label: '', value: '' },
  { label: 'ナレッジデータベース', value: '1' },
  { label: 'ウェブ検索', value: '2' },
  { label: '不明', value: '0' },
  { label: '見つからない', value: '99' }
]

// Options for RAG patterns (based on analyzed action types)
const ragPatternOptions = [
  { label: '', value: '' },
  { label: '通常', value: '1' },
  { label: 'RAG', value: '2' },
  { label: '要約', value: '3' },
  { label: '天気', value: '101' },
  { label: 'セマンテックキャッシュ', value: '199' }
]

// Options for normal processed status
const normalProcessedOptions = [
  { label: '', value: '' },
  { label: '処理済み', value: 'true' },
  { label: '未処理', value: 'false' }
]

const uiClass = {
  formGroup: 'grid grid-cols-12 gap-2 items-center'
}

const ui = {
  formGroup: {
    container: 'col-span-7',
    inner: 'col-span-5'
  }
}
const emit = defineEmits(['close', 'search'])

const isOpen = computed({
  get: () => props.show,
  set: (value) => {
    if (value === false) emit('close')
  }
})

// Validation function for advanced search
const validateAdvancedSearch = () => {
  try {
    advancedSearchSchema.parse(advancedSearch)
    return { success: true, errors: [] }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error.errors }
    }
    return { success: false, errors: [{ message: '予期しないエラーが発生しました' }] }
  }
}

// Function to perform search
const onSearch = () => {
  const validation = validateAdvancedSearch()

  if (!validation.success) {
    toast.add({
      title: 'エラー',
      description: validation.errors[0]?.message || '検索条件に誤りがあります',
      color: 'red'
    })
    return
  }

  // Emit search event with validated data
  emit('search', advancedSearch)
}
</script>

<template>
  <UModal
    v-model="isOpen"
    :ui="{
      height: 'max-h-[80vh]',
      base: 'scrollbar-thin',
      width: 'w-full sm:max-w-screen-md'
    }"
  >
    <UCard
      :ui="{
        ring: '',
        divide: 'divide-y divide-gray-100 dark:divide-gray-800',
        body: {
          padding: '!p-0'
        }
      }"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <div>高度な検索</div>
        </div>
      </template>

      <div class="flex flex-col gap-5 px-4 py-6">
        <!-- Advanced Search Form -->
        <UForm
          :schema="advancedSearchSchema"
          :state="advancedSearch"
          class="space-y-4"
        >
          <UFormGroup
            name="range"
            label="日付"
            description="日付範囲を指定できます。"
            :class="uiClass.formGroup"
            :ui="ui.formGroup"
          >
            <BaseDateRangePicker
              v-model="advancedSearch.range"
              :exclude-today="true"
              color="white"
              variant="solid"
              size="sm"
              class="w-full"
              icon="i-heroicons-calendar-20-solid"
            />
          </UFormGroup>

          <UFormGroup
            name="session_id"
            label="セッションID"
            description="フルセッションIDを指定できます。"
            :class="uiClass.formGroup"
            :ui="ui.formGroup"
          >
            <BaseUUIDInput
              v-model="advancedSearch.session_id"
              icon="emojione-monotone:id-button"
              size="sm"
              placeholder="例: 123e4567-e89b-12d3-a456-************"
              @keydown.esc="$event.target.blur()"
            />
          </UFormGroup>

          <UFormGroup
            name="chat_id"
            label="チャットID"
            description="チャットIDを指定できます。"
            :class="uiClass.formGroup"
            :ui="ui.formGroup"
          >
            <BaseUUIDInput
              v-model="advancedSearch.chat_id"
              icon="emojione-monotone:id-button"
              size="sm"
              placeholder="例: 123e4567-e89b-12d3-a456-************"
              @keydown.esc="$event.target.blur()"
            />
          </UFormGroup>

          <UFormGroup
            name="knowledge_type"
            label="ナレッジの種類"
            description="ナレッジの種類を指定できます。"
            :class="uiClass.formGroup"
            :ui="ui.formGroup"
          >
            <USelect
              v-model="advancedSearch.knowledge_type"
              :options="knowledgeTypeOptions"
              option-attribute="label"
              value-attribute="value"
              placeholder="ナレッジの種類を選択"
              size="sm"
            />
          </UFormGroup>

          <UFormGroup
            name="rag_pattern"
            label="RAGパターン"
            description="RAGパターンを指定できます。"
            :class="uiClass.formGroup"
            :ui="ui.formGroup"
          >
            <USelect
              v-model="advancedSearch.rag_pattern"
              :options="ragPatternOptions"
              option-attribute="label"
              value-attribute="value"
              placeholder="RAGパターンを選択"
              size="sm"
            />
          </UFormGroup>

          <UFormGroup
            name="normal_processed"
            label="正常処理済み"
            description="正常処理済みの状態を指定できます。"
            :class="uiClass.formGroup"
            :ui="ui.formGroup"
          >
            <USelect
              v-model="advancedSearch.normal_processed"
              :options="normalProcessedOptions"
              option-attribute="label"
              value-attribute="value"
              placeholder="処理状態を選択"
              size="sm"
            />
          </UFormGroup>
        </UForm>
      </div>
      {{ advancedSearch }}
      <template #footer>
        <div class="flex flex-row justify-between items-center">
          <div />
          <div class="flex justify-end gap-3">
            <UButton
              label="キャンセル"
              color="gray"
              @click="emit('close')"
            />
            <UButton
              label="検索"
              color="primary"
              class="w-24 justify-center"
              @click="onSearch"
            />
          </div>
        </div>
      </template>
    </UCard>
  </UModal>
</template>
