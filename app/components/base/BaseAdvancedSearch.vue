<script setup lang="ts">
import { z } from 'zod'
import type { FormSubmitEvent } from '#ui/types'

// UUID validation regex
const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i

// Zod schema for advanced search form
const advancedSearchSchema = z.object({
  range: z.object({
    start: z.date(),
    end: z.date()
  }),
  session_id: z
    .string()
    .optional()
    .refine(
      val => !val || val.trim() === '' || uuidRegex.test(val.trim()),
      {
        message: 'セッションIDは有効なUUID形式である必要があります'
      }
    ),
  chat_id: z
    .string()
    .optional()
    .refine(
      val => !val || val.trim() === '' || uuidRegex.test(val.trim()),
      {
        message: 'チャットIDは有効なUUID形式である必要があります'
      }
    ),
  context_type: z.string().optional(),
  analyzed_action: z.string().optional(),
  processed: z.string().optional()
})

// Bulk update form schema
const bulkUpdateSchema = z.object({
  priority: z
    .string()
    .optional()
    .refine(
      val => !val || val.trim() === '' || (!isNaN(Number(val)) && Number(val) > 0),
      {
        message: '優先度は正の数値である必要があります'
      }
    ),
  labels: z.array(z.string()).optional(),
  enabled: z.string().optional()
})

type AdvancedSearchSchema = z.output<typeof advancedSearchSchema>
type BulkUpdateSchema = z.output<typeof bulkUpdateSchema>

const { selectedTenantId, selectedEnvId } = useApp()
const trainingDatasStore = useTrainingDatasStore()
const { loadings } = storeToRefs(trainingDatasStore)
const route = useRoute()
const props = defineProps({
  show: Boolean,
  targets: Array as PropType<any[]>
})
const toast = useToast()

// Initialize with default date range (last 7 days)
const today = new Date()
const sevenDaysAgo = new Date()
sevenDaysAgo.setDate(today.getDate() - 7)

const advancedSearch = reactive({
  range: {
    start: sevenDaysAgo,
    end: today
  },
  session_id: '',
  chat_id: '',
  context_type: '',
  analyzed_action: '',
  processed: ''
})

const bulkUpdateForm = reactive({
  priority: '',
  labels: [] as string[],
  enabled: '' as string
})
watch(
  () => props.show,
  (value) => {
    if (value) {
      Object.assign(bulkUpdateForm, {
        priority: '',
        labels: [],
        enabled: ''
      })
    }
  }
)
const uiClass = {
  formGroup: 'grid grid-cols-12 gap-2 items-center'
}

const ui = {
  formGroup: {
    container: 'col-span-7',
    inner: 'col-span-5'
  }
}
const emit = defineEmits(['close', 'search'])

const isOpen = computed({
  get: () => props.show,
  set: (value) => {
    if (value === false) emit('close')
  }
})

// Validation function for advanced search
const validateAdvancedSearch = () => {
  try {
    advancedSearchSchema.parse(advancedSearch)
    return { success: true, errors: [] }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error.errors }
    }
    return { success: false, errors: [{ message: '予期しないエラーが発生しました' }] }
  }
}

// Validation function for bulk update
const validateBulkUpdate = () => {
  try {
    bulkUpdateSchema.parse(bulkUpdateForm)
    return { success: true, errors: [] }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error.errors }
    }
    return { success: false, errors: [{ message: '予期しないエラーが発生しました' }] }
  }
}

// Function to perform search
const onSearch = () => {
  const validation = validateAdvancedSearch()

  if (!validation.success) {
    toast.add({
      title: 'エラー',
      description: validation.errors[0]?.message || '検索条件に誤りがあります',
      color: 'red'
    })
    return
  }

  // Emit search event with validated data
  emit('search', advancedSearch)
}

const onSubmit = async () => {
  // Validate both forms
  const advancedSearchValidation = validateAdvancedSearch()
  const bulkUpdateValidation = validateBulkUpdate()

  if (!advancedSearchValidation.success) {
    toast.add({
      title: 'エラー',
      description: advancedSearchValidation.errors[0]?.message || '検索条件に誤りがあります',
      color: 'red'
    })
    return
  }

  if (!bulkUpdateValidation.success) {
    toast.add({
      title: 'エラー',
      description: bulkUpdateValidation.errors[0]?.message || '更新内容に誤りがあります',
      color: 'red'
    })
    return
  }

  let bulkUpdates = {}
  if (bulkUpdateForm.priority) {
    bulkUpdates = {
      ...bulkUpdates,
      priority: +bulkUpdateForm.priority
    }
  }
  if (bulkUpdateForm.labels.length) {
    bulkUpdates = {
      ...bulkUpdates,
      labels: bulkUpdateForm.labels
    }
  }
  if (bulkUpdateForm.enabled) {
    bulkUpdates = {
      ...bulkUpdates,
      enabled: bulkUpdateForm.enabled === '有効'
    }
  }
  const result = await trainingDatasStore.bulkUpdateKnowledges(
    props.targets || [],
    bulkUpdates
  )
  if (result) {
    isOpen.value = false
    toast.add({
      title: '成功',
      description: 'ナレッジを一括編集しました。',
      color: 'green'
    })
    trainingDatasStore.fetchTrainingDataDetail(
      route.params.id as string,
      selectedTenantId.value,
      selectedEnvId.value
    )
  }
}
</script>

<template>
  <UModal
    v-model="isOpen"
    :ui="{
      height: 'max-h-[80vh]',
      base: 'scrollbar-thin',
      width: 'w-full sm:max-w-screen-md'
    }"
  >
    <UCard
      :ui="{
        ring: '',
        divide: 'divide-y divide-gray-100 dark:divide-gray-800',
        body: {
          padding: '!p-0'
        }
      }"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <div>高度な検索</div>
        </div>
      </template>

      <div class="flex flex-col gap-5 px-4 py-6">
        <!-- Advanced Search Form -->
        <UForm
          :schema="advancedSearchSchema"
          :state="advancedSearch"
          class="space-y-4"
        >
          <UFormGroup
            name="range"
            label="日付"
            description="日付範囲を指定できます。"
            :class="uiClass.formGroup"
            :ui="ui.formGroup"
          >
            <BaseDateRangePicker
              v-model="advancedSearch.range"
              :exclude-today="true"
              color="white"
              variant="solid"
              size="sm"
              class="w-full justify-between"
              icon="i-heroicons-calendar-20-solid"
            />
          </UFormGroup>

          <UFormGroup
            name="session_id"
            label="セッションID"
            description="フルセッションIDを指定できます。"
            :class="uiClass.formGroup"
            :ui="ui.formGroup"
          >
            <BaseUUIDInput
              v-model="advancedSearch.session_id"
              icon="emojione-monotone:id-button"
              size="sm"
              placeholder="例: 123e4567-e89b-12d3-a456-************"
              @keydown.esc="$event.target.blur()"
            />
          </UFormGroup>

          <UFormGroup
            name="chat_id"
            label="チャットID"
            description="チャットIDを指定できます。"
            :class="uiClass.formGroup"
            :ui="ui.formGroup"
          >
            <BaseUUIDInput
              v-model="advancedSearch.chat_id"
              icon="emojione-monotone:id-button"
              size="sm"
              placeholder="例: 123e4567-e89b-12d3-a456-************"
              @keydown.esc="$event.target.blur()"
            />
          </UFormGroup>
        </UForm>

        <!-- Bulk Update Form -->
        <UForm
          :schema="bulkUpdateSchema"
          :state="bulkUpdateForm"
          class="space-y-4"
        >
          <UFormGroup
            name="enabled"
            label="ステータス"
            description="ナレッジの有効/無効を設定してください。"
            :class="uiClass.formGroup"
            :ui="ui.formGroup"
          >
            <USelect
              v-model="bulkUpdateForm.enabled"
              :options="['', '有効', '無効']"
              placeholder="有効/無効"
            />
          </UFormGroup>
        </UForm>
      </div>

      <template #footer>
        <div class="flex flex-row justify-between items-center">
          <div />
          <div class="flex justify-end gap-3">
            <UButton
              label="キャンセル"
              color="gray"
              @click="emit('close')"
            />
            <UButton
              :loading="loadings.bulkUpdateKnowledges"
              label="確定"
              color="primary"
              class="w-24 justify-center"
              @click="onSubmit"
            />
          </div>
        </div>
      </template>
    </UCard>
  </UModal>
</template>
