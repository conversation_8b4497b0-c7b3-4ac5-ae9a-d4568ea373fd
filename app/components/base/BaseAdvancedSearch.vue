<script setup lang="ts">
const { selectedTenantId, selectedEnvId } = useApp()
const trainingDatasStore = useTrainingDatasStore()
const { loadings } = storeToRefs(trainingDatasStore)
const route = useRoute()
const props = defineProps({
  show: Bo<PERSON>an,
  targets: Array as PropType<any[]>
})
const toast = useToast()
const advancedSearch = ref({
  range: {
    start: null,
    end: null
  },
  session_id: '',
  chat_id: '',
  context_type: '',
  analyzed_action: '',
  processed: ''
})
const bulkUpdateForm = ref({
  priority: '',
  labels: [],
  enabled: null
})
watch(
  () => props.show,
  (value) => {
    if (value) {
      bulkUpdateForm.value = {
        priority: '',
        labels: [],
        enabled: null
      }
    }
  }
)
const uiClass = {
  formGroup: 'grid grid-cols-12 gap-2 items-center'
}

const ui = {
  formGroup: {
    container: 'col-span-7',
    inner: 'col-span-5'
  }
}
const emit = defineEmits(['close'])

const isOpen = computed({
  get: () => props.show,
  set: (value) => {
    if (value === false) emit('close')
  }
})

const onSubmit = async () => {
  let bulkUpdates = {}
  if (bulkUpdateForm.value.priority) {
    bulkUpdates = {
      ...bulkUpdates,
      priority: +bulkUpdateForm.value.priority
    }
  }
  if (bulkUpdateForm.value.labels.length) {
    bulkUpdates = {
      ...bulkUpdates,
      labels: bulkUpdateForm.value.labels
    }
  }
  if (bulkUpdateForm.value.enabled) {
    bulkUpdates = {
      ...bulkUpdates,
      enabled: bulkUpdateForm.value.enabled === '有効'
    }
  }
  const result = await trainingDatasStore.bulkUpdateKnowledges(
    props.targets || [],
    bulkUpdates
  )
  if (result) {
    isOpen.value = false
    toast.add({
      title: '成功',
      description: 'ナレッジを一括編集しました。',
      color: 'green'
    })
    trainingDatasStore.fetchTrainingDataDetail(
      route.params.id as string,
      selectedTenantId.value,
      selectedEnvId.value
    )
  }
}
</script>

<template>
  <UModal
    v-model="isOpen"
    :ui="{
      height: 'max-h-[80vh]',
      base: 'scrollbar-thin',
      width: 'w-full sm:max-w-screen-md'
    }"
  >
    <UCard
      :ui="{
        ring: '',
        divide: 'divide-y divide-gray-100 dark:divide-gray-800',
        body: {
          padding: '!p-0'
        }
      }"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <div>高度な検索</div>
        </div>
      </template>

      <div class="flex flex-col gap-5 px-4 py-6">
        <UFormGroup
          name="priority"
          label="日付"
          description="日付範囲を指定できます。"
          :class="uiClass.formGroup"
          :ui="ui.formGroup"
        >
          <BaseDateRangePicker
            v-model="advancedSearch.range"
            :exclude-today="true"
            color="white"
            variant="solid"
            size="sm"
            class="w-full justify-between"
            icon="i-heroicons-calendar-20-solid"
          />
        </UFormGroup>
        <UFormGroup
          v-if="bulkUpdateForm.labels"
          name="label"
          label="セッションID"
          description="フルセッションIDを指定できます。"
          :class="uiClass.formGroup"
          :ui="ui.formGroup"
        >
          <BaseUUIDInput
            v-model="advancedSearch.session_id"
            icon="emojione-monotone:id-button"
            size="sm"
            placeholder="例: 123e4567-e89b-12d3-a456-************"
            @keydown.esc="$event.target.blur()"
          />
        </UFormGroup>
        <UFormGroup
          v-if="bulkUpdateForm.labels"
          name="label"
          label="チャットID"
          description="チャットIDを指定できます。"
          :class="uiClass.formGroup"
          :ui="ui.formGroup"
        >
          <BaseUUIDInput
            v-model="advancedSearch.chat_id"
            icon="emojione-monotone:id-button"
            size="sm"
            placeholder="例: 123e4567-e89b-12d3-a456-************"
            @keydown.esc="$event.target.blur()"
          />
        </UFormGroup>
        <UFormGroup
          name="enabled"
          label="ステータス"
          description="ナレッジの有効/無効を設定してください。"
          :class="uiClass.formGroup"
          :ui="ui.formGroup"
        >
          <USelect
            v-model="bulkUpdateForm.enabled"
            :options="['', '有効', '無効']"
            placeholder="有効/無効"
          />
        </UFormGroup>
      </div>

      <template #footer>
        <div class="flex flex-row justify-between items-center">
          <div />
          <div class="flex justify-end gap-3">
            <UButton
              label="キャンセル"
              color="gray"
              @click="emit('close')"
            />
            <UButton
              :loading="loadings.bulkUpdateKnowledges"
              label="確定"
              color="primary"
              class="w-24 justify-center"
              @click="onSubmit"
            />
          </div>
        </div>
      </template>
    </UCard>
  </UModal>
</template>
